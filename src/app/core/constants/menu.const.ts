import { IMenu } from '@core/models';
import { ROLE_ACCOUNT } from './account.const';
import { ROLE_AI } from './ai.const';
import { ADMIN_PATH, APP_PATH, APP_ROUTES, STUDIO_PATH } from './routes.const';

export const MENU_ITEMS: IMenu[] = [
  {
    id: 'dashboard',
    title: 'Dashboard',
    link: APP_PATH.DASHBOARD,
    icon: 'icDashboard',
    roles: [
      ROLE_ACCOUNT.USER,
      ROLE_ACCOUNT.ADMIN,
      ROLE_ACCOUNT.PARTNER,
      ROLE_ACCOUNT.SUPER_ADMIN,
    ],
    rolesAI: [ROLE_AI.SUPPORT, ROLE_AI.EDITOR, ROLE_AI.ADMIN, ROLE_AI.OWNER],
  },
  {
    id: 'studio',
    title: 'Studio',
    icon: 'icStudio',
    roles: [
      ROLE_ACCOUNT.USER,
      ROLE_ACCOUNT.ADMIN,
      ROLE_ACCOUNT.PARTNER,
      ROLE_ACCOUNT.SUPER_ADMIN,
    ],
    rolesAI: [ROLE_AI.ADMIN, ROLE_AI.OWNER],
    children: [
      {
        id: 'studio-builder',
        title: 'Builder',
        icon: 'icStudio',
        link: APP_ROUTES.STUDIO + '/' + STUDIO_PATH.BUILDER,
        roles: [
          ROLE_ACCOUNT.USER,
          ROLE_ACCOUNT.ADMIN,
          ROLE_ACCOUNT.PARTNER,
          ROLE_ACCOUNT.SUPER_ADMIN,
        ],
        rolesAI: [ROLE_AI.ADMIN, ROLE_AI.OWNER],
      },
      {
        id: 'studio-api',
        title: 'API',
        icon: 'icApi',
        link: APP_ROUTES.STUDIO + '/' + STUDIO_PATH.API,
        roles: [
          ROLE_ACCOUNT.USER,
          ROLE_ACCOUNT.ADMIN,
          ROLE_ACCOUNT.PARTNER,
          ROLE_ACCOUNT.SUPER_ADMIN,
        ],
        rolesAI: [ROLE_AI.ADMIN, ROLE_AI.OWNER],
      },
      {
        id: 'studio-function',
        title: 'Function',
        icon: 'icFunction',
        link: APP_ROUTES.STUDIO + '/' + STUDIO_PATH.FUNCTION,
        roles: [
          ROLE_ACCOUNT.USER,
          ROLE_ACCOUNT.ADMIN,
          ROLE_ACCOUNT.PARTNER,
          ROLE_ACCOUNT.SUPER_ADMIN,
        ],
        rolesAI: [ROLE_AI.ADMIN, ROLE_AI.OWNER],
      },
      {
        id: 'studio-event',
        title: 'Event',
        icon: 'icEvent',
        link: APP_ROUTES.STUDIO + '/' + STUDIO_PATH.EVENT,
        roles: [
          ROLE_ACCOUNT.USER,
          ROLE_ACCOUNT.ADMIN,
          ROLE_ACCOUNT.PARTNER,
          ROLE_ACCOUNT.SUPER_ADMIN,
        ],
        rolesAI: [ROLE_AI.ADMIN, ROLE_AI.OWNER],
      },
    ],
  },
  {
    id: 'inbox',
    title: 'Inbox',
    link: APP_PATH.INBOX,
    icon: 'icInbox',
    roles: [
      ROLE_ACCOUNT.USER,
      ROLE_ACCOUNT.ADMIN,
      ROLE_ACCOUNT.PARTNER,
      ROLE_ACCOUNT.SUPER_ADMIN,
    ],
    rolesAI: [ROLE_AI.SUPPORT, ROLE_AI.EDITOR, ROLE_AI.ADMIN, ROLE_AI.OWNER],
  },
  {
    id: 'knowledge-base',
    title: 'Knowledge Base',
    link: APP_PATH.KNOWLEDGE_BASE,
    icon: 'icKnowledgeBase',
    roles: [
      ROLE_ACCOUNT.USER,
      ROLE_ACCOUNT.ADMIN,
      ROLE_ACCOUNT.PARTNER,
      ROLE_ACCOUNT.SUPER_ADMIN,
    ],
    rolesAI: [ROLE_AI.EDITOR, ROLE_AI.ADMIN, ROLE_AI.OWNER],
  },
  {
    id: 'integrations',
    title: 'Integrations',
    link: APP_PATH.INTEGRATIONS,
    icon: 'icIntegration',
    roles: [
      ROLE_ACCOUNT.USER,
      ROLE_ACCOUNT.ADMIN,
      ROLE_ACCOUNT.PARTNER,
      ROLE_ACCOUNT.SUPER_ADMIN,
    ],
    rolesAI: [ROLE_AI.ADMIN, ROLE_AI.OWNER],
  },
  {
    id: 'tags',
    title: 'Tags',
    link: APP_PATH.TAG,
    icon: 'icTag',
    roles: [
      ROLE_ACCOUNT.PARTNER,
      ROLE_ACCOUNT.USER,
      ROLE_ACCOUNT.ADMIN,
      ROLE_ACCOUNT.SUPER_ADMIN,
    ],
    rolesAI: [ROLE_AI.ADMIN, ROLE_AI.OWNER],
  },
  {
    id: 'leads',
    title: 'Leads',
    link: APP_PATH.LEADS,
    icon: 'icLeads',
    roles: [
      ROLE_ACCOUNT.USER,
      ROLE_ACCOUNT.ADMIN,
      ROLE_ACCOUNT.PARTNER,
      ROLE_ACCOUNT.SUPER_ADMIN,
    ],
    rolesAI: [ROLE_AI.ADMIN, ROLE_AI.OWNER],
  },
  {
    id: 'faq',
    title: 'FAQ',
    link: APP_PATH.FAQ,
    icon: 'icFAQ',
    roles: [
      ROLE_ACCOUNT.USER,
      ROLE_ACCOUNT.PARTNER,
      ROLE_ACCOUNT.ADMIN,
      ROLE_ACCOUNT.SUPER_ADMIN,
    ],
    rolesAI: [ROLE_AI.ADMIN, ROLE_AI.OWNER],
  },
  {
    id: 'settings',
    title: 'Settings',
    link: APP_PATH.SETTINGS,
    icon: 'icSettings',
    roles: [
      ROLE_ACCOUNT.USER,
      ROLE_ACCOUNT.ADMIN,
      ROLE_ACCOUNT.PARTNER,
      ROLE_ACCOUNT.SUPER_ADMIN,
    ],
    rolesAI: [ROLE_AI.ADMIN, ROLE_AI.OWNER],
  },
  {
    id: 'old-settings',
    title: 'Settings 2',
    link: APP_PATH.SETTINGS_V2,
    icon: 'icSettings',
    roles: [
      ROLE_ACCOUNT.USER,
      ROLE_ACCOUNT.ADMIN,
      ROLE_ACCOUNT.PARTNER,
      ROLE_ACCOUNT.SUPER_ADMIN,
    ],
    rolesAI: [ROLE_AI.ADMIN, ROLE_AI.OWNER],
  },
  {
    id: 'preview',
    title: 'Preview',
    link: APP_PATH.PREVIEW,
    icon: 'icPreview',
    roles: [
      ROLE_ACCOUNT.USER,
      ROLE_ACCOUNT.ADMIN,
      ROLE_ACCOUNT.SUPER_ADMIN,
      ROLE_ACCOUNT.PARTNER,
    ],
    rolesAI: [ROLE_AI.EDITOR, ROLE_AI.ADMIN, ROLE_AI.OWNER],
  },
  {
    id: 'admin',
    title: 'Admin',
    icon: 'icAdmin',
    roles: [ROLE_ACCOUNT.ADMIN, ROLE_ACCOUNT.PARTNER, ROLE_ACCOUNT.SUPER_ADMIN],
    rolesAI: [ROLE_AI.ADMIN, ROLE_AI.OWNER],
    children: [
      {
        id: 'admin-user',
        title: 'Users',
        icon: 'icUser',
        link: APP_ROUTES.ADMIN + '/' + ADMIN_PATH.USER,
        roles: [
          ROLE_ACCOUNT.ADMIN,
          ROLE_ACCOUNT.PARTNER,
          ROLE_ACCOUNT.SUPER_ADMIN,
        ],
        rolesAI: [ROLE_AI.ADMIN, ROLE_AI.OWNER],
      },
      {
        id: 'admin-plan',
        title: 'Plan',
        icon: 'icPlan',
        link: APP_ROUTES.ADMIN + '/' + ADMIN_PATH.PLAN,
        roles: [
          ROLE_ACCOUNT.ADMIN,
          ROLE_ACCOUNT.PARTNER,
          ROLE_ACCOUNT.SUPER_ADMIN,
        ],
        rolesAI: [ROLE_AI.ADMIN, ROLE_AI.OWNER],
      },
      {
        id: 'admin-payment',
        title: 'Payment',
        icon: 'icPayment',
        link: APP_ROUTES.ADMIN + '/' + ADMIN_PATH.PAYMENT_METHOD,
        roles: [ROLE_ACCOUNT.SUPER_ADMIN, ROLE_ACCOUNT.ADMIN],
        rolesAI: [ROLE_AI.ADMIN, ROLE_AI.OWNER],
      },
      // {
      //   id: 'admin-flush',
      //   title: 'Flush redis',
      //   link: APP_ROUTES.ADMIN + '/' + ADMIN_PATH.SETTINGS,
      //   roles: [ROLE_ACCOUNT.SUPER_ADMIN],
      //   rolesAI: [
      //     ROLE_AI.ADMIN,
      //     ROLE_AI.OWNER,
      //   ],
      // },
    ],
  },
];
