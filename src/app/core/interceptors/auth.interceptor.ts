import {
  HttpErrorResponse,
  HttpHandlerFn,
  HttpInterceptorFn,
  HttpRequest,
} from '@angular/common/http';
import { inject } from '@angular/core';
import { Router } from '@angular/router';
import { APP_ROUTES, AUTH_PATH } from '@core/constants';
import { AuthService } from '@core/services';
import { DxSnackBar } from '@dx-ui/ui';
import { AuthUtils } from '@shared/utils';
import { catchError, throwError } from 'rxjs';

export const authInterceptor: HttpInterceptorFn = (
  request: HttpRequest<unknown>,
  next: HttpHandlerFn
) => {
  const authService = inject(AuthService);
  const snackBar = inject(DxSnackBar);
  const router = inject(Router);

  let newReq = request;
  const token = authService.accessToken;

  if (token && !AuthUtils.isTokenExpired(token)) {
    newReq = request.clone({
      headers: request.headers.set('Authorization', `Bearer ${token}`),
    });
  }

  return next(newReq).pipe(
    catchError((error) => {
      snackBar.open(
        typeof error.error?.detail === 'string'
          ? error.error.detail
          : 'Unknown error',
        '',
        {
          panelClass: 'dx-snack-bar-error',
          duration: 5000,
          verticalPosition: 'top',
          horizontalPosition: 'right',
        }
      );

      if (error instanceof HttpErrorResponse && error.status === 401) {
        authService.logout();
        void router.navigate([APP_ROUTES.AUTH + '/' + AUTH_PATH.LOGIN]);
      }

      return throwError(() => error);
    })
  );
};
