@if (!uiStore.isHandset()) {
<dx-sidenav-container class="h-dvh">
  <dx-sidenav
    #leftSidenav
    [mode]="leftSideNavMode()"
    [position]="'start'"
    [opened]="leftSideNavOpened()"
  >
    <div class="h-full w-full px-3 pb-5 pt-5.5">
      <app-svg-icon
        [type]="uiStore.theme() === 'light' ? 'icLogo' : 'icLogoDark'"
        class="w-41 h-10"
      ></app-svg-icon>
      <app-ai-select></app-ai-select>
      <ul class="mt-2 scrollable-menu">
        @for (item of menuItems; track item.id) { @if (item.children) {
        <app-menu-item-multi
          *hasRoles="{
            userRoles: item?.roles ?? [],
            aiRoles: item?.rolesAI ?? []
          }"
          [menuItem]="item"
        ></app-menu-item-multi>
        } @else {
        <app-menu-item
          *hasRoles="{
            userRoles: item?.roles ?? [],
            aiRoles: item?.rolesAI ?? []
          }"
          [menuItem]="item"
          [ngClass]="{ 'xl:hidden': item.link === APP_ROUTES.PREVIEW }"
        ></app-menu-item>
        } }
      </ul>
      <div
        class="absolute left-0 bottom-0 bg-base-200 dark:bg-dark-base-200 w-full p-3 pt-0 flex flex-col"
      >
        <!--        Need help-->
        <div
          (click)="toggleNeedHelp()"
          cdkOverlayOrigin
          #needHelpOrigin
          class="w-full px-3 py-[10px] rounded-xl cursor-pointer hover:bg-base-100 dark:hover:bg-dark-base-100 mb-2"
        >
          <div
            class="w-full flex items-center space-x-3 text-[15px] text-neutral-content dark:text-dark-neutral-content"
          >
            <app-svg-icon
              type="icHeadPhone"
              class="w-6 h-6 text-2xl text-neutral-content dark:text-dark-neutral-content"
            ></app-svg-icon>
            <div class="flex justify-center font-medium text-[15px]">
              Need help
            </div>
          </div>
          <ng-template
            cdkConnectedOverlay
            [cdkConnectedOverlayOrigin]="needHelpOrigin"
            [cdkConnectedOverlayOpen]="showOptionNeedHelp()"
            [cdkConnectedOverlayPositions]="[
              {
                originX: 'start',
                originY: 'top',
                overlayX: 'start',
                overlayY: 'bottom',
                offsetY: 5
              }
            ]"
          >
            <ul
              (clickOutside)="showOptionNeedHelp.set(false)"
              class="w-[245px] flex flex-col justify-between p-1 z-50 shadow-lg rounded-xl !text-base-content dark:!text-dark-base-content border border-primary-border dark:border-dark-primary-border bg-base-400 dark:bg-dark-base-400"
            >
              <li>
                <div
                  class="flex px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg hover:bg-base-100 dark:hover:bg-dark-base-100"
                >
                  <!--                  <ng-icon name="heroQuestionMarkCircle" class="text-2xl flex items-center justify-center"></ng-icon>-->
                  <div
                    class="flex items-center justify-between text-[16px] font-medium"
                  >
                    <p>
                      <a
                        href="https://docs.dxconnect.lifesup.ai/"
                        target="_blank"
                        >Documentation</a
                      >
                    </p>
                  </div>
                </div>
              </li>
              <li>
                <div
                  class="flex px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg hover:bg-base-100 dark:hover:bg-dark-base-100"
                >
                  <!--                  <ng-icon name="heroAtSymbol" class="text-2xl flex items-center justify-center"></ng-icon>-->
                  <div
                    class="flex items-center justify-between text-[16px] font-medium"
                  >
                    <p>
                      <a href="mailto:<EMAIL>" target="_blank"
                        >Email us</a
                      >
                    </p>
                  </div>
                </div>
              </li>
            </ul>
          </ng-template>
        </div>
        <div
          class="pb-2 border-b border-b-primary-border dark:border-b-dark-primary-border mb-2"
        >
          <app-theme-toggle></app-theme-toggle>
        </div>
        <app-user-info-config></app-user-info-config>
      </div>
    </div>
  </dx-sidenav>

  <dx-sidenav
    #rightSidenav
    [mode]="'side'"
    [position]="'end'"
    [opened]="showPreview() && rightSideNavOpened()"
    class="dx-sidenav-preview"
  >
    <div class="h-full overflow-y-auto bg-transparent py-6 pr-8">
      @defer (on viewport) { @if (previewKey() % 2 === 0) {
      <app-preview></app-preview>
      } @if (previewKey() % 2 === 1) {
      <app-preview></app-preview>
      } } @placeholder {
      <div class="h-full w-full bg-gray-200"></div>
      }
    </div>
  </dx-sidenav>

  <dx-sidenav-content
    class="p-4 md:py-6 md:px-10"
    [class.md:pr-4]="showPreview() && rightSideNavOpened()"
  >
    <router-outlet></router-outlet>
  </dx-sidenav-content>
</dx-sidenav-container>
} @else {
<div>
  <main
    class="w-full h-full my-0 mx-auto"
    [ngClass]="{
      '!pb-0': mHideNav(),
      '!pb-18': !mHideNav(),
      'bg-container-background': uiStore.theme() === 'light',
      'bg-dark-container-background': uiStore.theme() === 'dark'
    }"
  >
    <router-outlet></router-outlet>
  </main>
  @if (!mHideNav()) {
  <app-mobile-nav></app-mobile-nav>
  }
</div>
}
