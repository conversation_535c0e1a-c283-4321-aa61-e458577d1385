/* List Plan Component Styles */

.container {
  max-width: 1200px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

h1 {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 0.5rem;
}

/* Search and filter styles */
.relative {
  position: relative;
}

/* Action button styles */
button.flex {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  cursor: pointer;
}

ng-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Status styles */
.bg-light-green {
  background-color: #4CAF50;
}

.bg-light-orange {
  background-color: #FF9800;
}

.bg-light-red {
  background-color: #F44336;
}

.text-light-green {
  color: #4CAF50;
}

.text-light-orange {
  color: #FF9800;
}

.text-light-red {
  color: #F44336;
}

/* Required field asterisk */
.label-input.required::after {
  content: '*';
  color: #FF0000;
  margin-left: 4px;
  font-weight: bold;
}

/* Custom styles for the asterisk in the label text */
.required-asterisk {
  color: #FF0000;
  font-weight: bold;
}
