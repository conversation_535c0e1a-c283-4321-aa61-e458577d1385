import { Component, HostListener, inject, signal } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { Router, RouterLink } from '@angular/router';
import { APP_ROUTES } from '@core/constants';
import { AuthService } from '@core/services';
import { UIStore } from '@core/stores';
import {
  DxButton,
  DxCheckbox,
  DxFormField,
  DxInput,
  DxLabel,
  DxSnackBar,
  DxSuffix,
} from '@dx-ui/ui';
import { SvgIconComponent } from '@shared/components';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [
    FormsModule,
    SvgIconComponent,
    DxFormField,
    DxLabel,
    DxInput,
    DxButton,
    DxCheckbox,
    RouterLink,
    DxSuffix,
  ],
  templateUrl: './login.component.html',
  styleUrl: './login.component.css',
})
export class LoginComponent {
  username: string = '';
  password: string = '';
  isRemember: boolean = false;

  showPassword = signal<boolean>(false);
  deniedAccess = signal<boolean>(false);
  isSigningIn = signal<boolean>(false);

  protected uiStore = inject(UIStore);
  private authService = inject(AuthService);
  private router = inject(Router);
  private snackBar = inject(DxSnackBar);

  togglePasswordVisibility() {
    this.showPassword.update((prev) => !prev);
  }

  @HostListener('document:keydown.enter', ['$event'])
  onEnterKey(event: KeyboardEvent) {
    const target = event.target as HTMLElement;
    if (target.tagName !== 'INPUT' && target.tagName !== 'TEXTAREA') {
      this.login();
    }
  }

  login() {
    if (this.isSigningIn()) return;
    this.isSigningIn.set(true);
    this.authService.login(this.username, this.password).subscribe({
      next: (res) => {
        if (res.access_token) {
          const storage = this.isRemember ? localStorage : sessionStorage;
          storage.setItem('accessToken', res.access_token);
          this.deniedAccess.set(false);
          this.snackBar.open('Sign in successfully', '', {
            panelClass: 'dx-snack-bar-success',
            duration: 5000,
            verticalPosition: 'top',
            horizontalPosition: 'right',
          });
          this.isSigningIn.set(false);
          void this.router.navigate([APP_ROUTES.DASHBOARD]);
        }
      },
      error: (error) => {
        if (error.status === 401) {
          this.deniedAccess.set(true);
        }
        this.isSigningIn.set(false);
      },
    });
  }
}
