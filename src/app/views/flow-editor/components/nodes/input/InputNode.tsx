// @ts-nocheck
import GoToBlockHandle from "@flow-editor/components/flow/GoToBlockHandle";
import NodeHeader from "@flow-editor/components/nodes/NodeHeader";
import { StyledNodeFlow } from "@flow-editor/components/styled";
import { InputNodeData, LayoutState } from "@flow-editor/model";
import {
    useLayoutState,
    useStudioState
} from "@flow-editor/store";
import { hexToRgb } from "@flow-editor/utils/util";
import React, { useState } from "react";
import { Handle, Position } from "reactflow";
import styled from "styled-components";

const StyledHandleSourceAnchor = styled.div<{ $bgColor, $data }>`
  margin-top: 10px;
  padding: 6px 8px;
  text-align: start;
  color: white;
  font-size: 10px;
  border-radius: 8px;
  font-style: normal;
  background: ${(props) =>
    props.$bgColor
      ? `rgba(${hexToRgb(props.$bgColor).r}, ${hexToRgb(props.$bgColor).g}, ${
        hexToRgb(props.$bgColor).b
      }, 0.7)`
      : "#7241ff"};
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;

const InputNode = ({data}: { data: InputNodeData }) => {
  const {status} = useStudioState(state => state);
  const [nodeSelected] = useState<boolean>(false);
  const {theme} = useLayoutState<LayoutState>(state => state);

  return (
    <StyledNodeFlow $selected={data.selected || nodeSelected} $nodeColor={data.node_color} $theme={theme}>
      <div className="absolute left-0 text-[10px] text-neutral-content dark:!text-dark-neutral-content" style={{top: -20}}>{data.id}</div>
      <NodeHeader data={data} iconName={data.icon}/>
      <div>
        <StyledHandleSourceAnchor $bgColor={data.node_color} $data={!!(data)}>
          Input
        </StyledHandleSourceAnchor>
        {
          !data.goToBlockSource ? (
            <Handle
              type="source"
              position={Position.Right}
              id={`${data.id}_source#1`}
              style={{
                height: 10,
                width: 10,
                backgroundColor: theme === 'dark' ? 'white' : 'black'
              }}
            />
          ) : (() => {
              const key = `${data.id}_source#1`
              const goToBlock = data.goToBlockSource.find(v => v.sourceHandle === key)
              if (goToBlock) {
                return <GoToBlockHandle
                  data={data}
                  sourceHandle={key}
                  style={{
                    height: 14,
                    width: 84,
                    right: -80,
                    backgroundColor: theme === 'dark' ? 'white' : 'black',
                    fontSize: 10,
                    color: theme === 'dark' ? 'black' : 'white',
                    border: 'none',
                    borderRadius: 10,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                />
              }

              return <Handle
                type="source"
                position={Position.Right}
                id={`${data.id}_source#1`}
                style={{
                  height: 10,
                  width: 10,
                  backgroundColor: theme === 'dark' ? 'white' : 'black'
                }}
              />
            }
          )()
        }
      </div>
    </StyledNodeFlow>
  );
};

export default InputNode;
