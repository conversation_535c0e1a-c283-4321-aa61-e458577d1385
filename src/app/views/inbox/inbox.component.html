@if (!uiStore.isHandset()) {
<h1
  class="text-[28px] font-bold text-base-content dark:text-dark-base-content flex items-center space-x-2"
>
  <span>Inbox</span>
  <app-svg-icon
    [type]="'heroInformationCircle'"
    dxTooltip="All conversations with your AI customer support."
    dxTooltipPosition="above"
    dxTooltipClass="multiline-tooltip cus-tooltip"
    class="w-6 h-6 mt-1"
  ></app-svg-icon>
</h1>
<p class="text-[15px] text-neutral-content dark:text-dark-neutral-content">
  All conversations with your AI customer support.
</p>

<dx-card appearance="outlined" class="block mt-6">
  <dx-card-header [style.--dx-card-padding]="'0px'">
    <div
      class="inbox-header 3xl:flex flex-wrap border-b border-b-primary-border dark:border-b-dark-primary-border text-base-content dark:text-dark-base-content p-4"
    >
      <div
        class="inbox-filter flex flex-wrap items-center gap-2"
        [ngClass]="{ active: isChatListActive() }"
      >
        <!-- Search by Name -->
        <div class="lg:w-48 flex items-center">
          <dx-form-field
            class="w-full"
            [style.margin-bottom]="0"
            [style.--dx-form-field-label-offset-y]="0"
            [subscriptHidden]="true"
          >
            <input
              dx-input
              type="text"
              placeholder="Search by Name"
              [ngModel]="filterName()"
              (ngModelChange)="filterName.set($event)"
              (input)="onNameSearchChange($event)"
            />
            <app-svg-icon
              dxPrefix
              type="icSearch"
              class="ml-3 w-6 h-6 !text-neutral-content dark:!text-dark-neutral-content"
            ></app-svg-icon>
          </dx-form-field>
        </div>

        <!-- Search by content -->
        <div class="lg:w-48 flex items-center">
          <dx-form-field
            class="w-full"
            [style.margin-bottom]="0"
            [style.--dx-form-field-label-offset-y]="0"
            [subscriptHidden]="true"
          >
            <input
              dx-input
              type="text"
              placeholder="Search by Content"
              [ngModel]="filterKeyWord()"
              (ngModelChange)="filterKeyWord.set($event)"
              (input)="onContentSearchChange($event)"
            />
            <app-svg-icon
              dxPrefix
              type="icSearch"
              class="ml-3 w-6 h-6 !text-neutral-content dark:!text-dark-neutral-content"
            ></app-svg-icon>
          </dx-form-field>
        </div>

        <!-- Date pickers -->
        <div class="flex items-center gap-x-2">
          <div class="relative">
            <div
              class="modern-date-field w-32 bg-base-400 dark:bg-dark-base-400 rounded-xl border border-gray-300 dark:border-gray-700 overflow-hidden"
            >
              <mat-form-field
                appearance="outline"
                class="w-full custom-mat-form-field"
              >
                <input
                  matInput
                  [matDatepicker]="pickerFrom"
                  [value]="timeFrom()"
                  (dateChange)="onDateFromMatChange($event)"
                  class="py-2 px-3"
                  [max]="timeTo()"
                />
                <mat-datepicker-toggle
                  matIconSuffix
                  [for]="pickerFrom"
                ></mat-datepicker-toggle>
                <mat-datepicker
                  #pickerFrom
                  [dateClass]="dateClass"
                ></mat-datepicker>
              </mat-form-field>
            </div>
          </div>

          <div class="relative">
            <div
              class="modern-date-field w-32 bg-base-400 dark:bg-dark-base-400 rounded-xl border border-gray-300 dark:border-gray-700 overflow-hidden"
            >
              <mat-form-field
                appearance="outline"
                class="w-full custom-mat-form-field"
              >
                <input
                  matInput
                  [matDatepicker]="pickerTo"
                  [value]="timeTo()"
                  (dateChange)="onDateToMatChange($event)"
                  class="py-2 px-3"
                  [min]="timeFrom()"
                  [max]="timeNow()"
                />
                <mat-datepicker-toggle
                  matIconSuffix
                  [for]="pickerTo"
                ></mat-datepicker-toggle>
                <mat-datepicker
                  #pickerTo
                  [dateClass]="dateClass"
                ></mat-datepicker>
              </mat-form-field>
            </div>
          </div>
        </div>

        <!-- Status select -->
        <div class="lg:w-32 flex items-center">
          <dx-form-field
            class="w-full"
            [style.margin-bottom]="0"
            [style.--dx-form-field-label-offset-y]="0"
            [subscriptHidden]="true"
          >
            <dx-select
              [ngModel]="filterStatus()"
              (ngModelChange)="filterStatus.set($event); doSearch()"
              placeholder="Select status"
            >
              @for (status of listStatus; track status.code) {
              <dx-option [value]="status.code">{{ status.label }}</dx-option>
              }
            </dx-select>
          </dx-form-field>
        </div>

        <dx-form-field
          [style.margin-bottom]="0"
          [style.--dx-form-field-label-offset-y]="0"
          [subscriptHidden]="true"
          class="w-full lg:w-40"
        >
          <dx-select
            [ngModel]="filterTags()"
            (ngModelChange)="selectTags($event)"
            placeholder="Search by Tags"
          >
            <dx-option [value]="0">All</dx-option>
            @for (tag of listTagForConversationFiltered(); track tag.id) {
            <dx-option [value]="tag.id">{{ tag.name }}</dx-option>
            } @empty {
            <dx-option [value]="">No option found</dx-option>
            }
          </dx-select>
        </dx-form-field>

        <div class="flex items-center justify-center h-12">
          <svg
            width="2"
            height="12"
            viewBox="0 0 2 12"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path d="M1 0V12" stroke="#D2D6DD" />
          </svg>
        </div>
        <!-- Export button -->
        <div class="flex items-center justify-center">
          <button
            dxButton="elevated"
            [style.height]="'48px'"
            (click)="openExportOptions.set(!openExportOptions())"
            cdkOverlayOrigin
            #exportOptions="cdkOverlayOrigin"
          >
            <div class="flex items-center justify-between space-x-1 !text-neutral-content dark:!text-dark-neutral-content">
              <app-svg-icon type="icDownload" class="w-6 h-6"></app-svg-icon>
              <span class="text-sm">Export</span>
            </div>
          </button>
          <ng-template
            cdkConnectedOverlay
            [cdkConnectedOverlayOrigin]="exportOptions"
            [cdkConnectedOverlayOpen]="openExportOptions()"
            [cdkConnectedOverlayWidth]="
              exportOptions.elementRef.nativeElement.offsetWidth
            "
            [cdkConnectedOverlayPositions]="[
              {
                originX: 'center',
                originY: 'bottom',
                overlayX: 'center',
                overlayY: 'top',
                offsetY: 5
              }
            ]"
          >
            <ul
              class="p-2 w-[245px] flex flex-col justify-between shadow-lg rounded-xl border border-primary-border dark:border-dark-primary-border bg-base-400 dark:bg-dark-base-400"
              (clickOutside)="openExportOptions.set(false)"
            >
              <li
                class="flex px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg hover:bg-base-100 dark:hover:bg-dark-base-100"
                (click)="exportUsingS3()"
              >
                <div
                  class="flex items-center justify-between text-[16px] font-medium !text-base-content dark:!text-dark-base-content"
                >
                  AWS S3
                </div>
              </li>
              <li
                class="flex px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg hover:bg-base-100 dark:hover:bg-dark-base-100"
              >
                <div
                  class="flex items-center justify-between text-[16px] font-medium !text-base-content dark:!text-dark-base-content"
                >
                  SFTP
                </div>
              </li>
            </ul>
          </ng-template>
        </div>
      </div>
    </div>
  </dx-card-header>

  <dx-card-content
    [style.--dx-card-padding]="'0px'"
    class="h-[calc(100dvh-180px)] lg:h-[calc(100dvh-220px)]"
  >
    <div class="w-full h-full">
      <div
        class="grid grid-cols-24 gap-0 h-[calc(100dvh-236px)] overflow-hidden conversation-list"
      >
        <!-- Conversation List Component -->
        <app-conversation-list
          class="col-span-24 lg:col-span-9 h-[calc(100dvh-236px)]"
          [class.hidden]="!isChatListActive()"
          [class.lg:block]="true"
          [conversations]="conversations()"
          [selectedConversationId]="selectedConversationId()"
          [isLoadingConversation]="isLoadingConversation()"
          [nameConversationNew]="nameConversationNew()"
          [hasMoreData]="hasMoreData()"
          [totalConversations]="totalConversations()"
          [searchContent]="searchContent()"
          [searchName]="searchName()"
          [timeFrom]="timeFrom()"
          [timeTo]="timeTo()"
          [listTagSelectedInConversation]="listTagSelectedInConversation()"
          [shouldPaginate]="true"
          [openExportOptions]="openExportOptions()"
          [isChatListActive]="isChatListActive()"
          (conversationSelected)="onConversationSelected($event)"
          (scrolled)="onConversationScrolled()"
          (nameConversationNewChange)="nameConversationNew.set($event)"
          (conversationsUpdated)="onConversationsUpdated($event)"
          (conversationStatusChanged)="onConversationStatusChanged($event)"
        ></app-conversation-list>

        <!-- Chat Content Component -->
        <app-chat-content
          class="col-span-24 lg:col-span-15"
          [class.hidden]="isChatListActive()"
          [class.lg:block]="true"
          [conversationId]="selectedConversationId()"
          [showMessageInbox]="showMessageInbox()"
          [isLoadingContentConversation]="isLoadingContentConversation()"
          [chatMessages]="chatMessages()"
          [isTyping]="isTyping()"
          [isChatWithAI]="isChatWithAI()"
          [message]="message()"
          [isSendingMessage]="isSendingMessage()"
          [listTagSelectedInConversation]="listTagSelectedInConversation()"
          [conversationStatusInfo]="conversationStatusInfo()"
          (messageChange)="onMessageChange($event)"
          (addTag)="onAddTag()"
          (openSaveFAQDialog)="onOpenSaveFAQDialog($event)"
          (messagesUpdated)="onMessagesUpdated($event)"
          (sendingStatusChange)="onSendingStatusChange($event)"
          (reloadChatMessages)="onReloadChatMessages()"
        ></app-chat-content>
      </div>
    </div>
  </dx-card-content>
</dx-card>
} @else {
<app-mobile-header [title]="'Inbox'" [hideBack]="true"></app-mobile-header>
<div class="w-full max-h-screen flex flex-col pt-18 overflow-hidden">
  <div class="flex items-center justify-between space-x-3 px-4 pb-4">
    <dx-form-field
      class="flex-1 w-full"
      [style.margin-bottom]="0"
      [style.--dx-form-field-label-offset-y]="0"
      [subscriptHidden]="true"
    >
      <input
        dx-input
        type="text"
        placeholder="Search by Name"
        [ngModel]="filterName()"
        (ngModelChange)="filterName.set($event)"
        (input)="onNameSearchChange($event)"
      />
      <app-svg-icon
        dxPrefix
        type="icSearch"
        class="ml-3 w-6 h-6 !text-neutral-content dark:!text-dark-neutral-content"
      ></app-svg-icon>
    </dx-form-field>

    <div class="flex-shrink-0 flex items-center space-x-1">
      <div
        class="w-10 h-10 rounded-full flex items-center justify-center bg-white dark:bg-black border border-primary-border dark:border-dark-primary-border"
        (click)="openExportOptions.set(!openExportOptions())"
        cdkOverlayOrigin
        #mExportOptions="cdkOverlayOrigin"
      >
        <svg
          width="25"
          height="24"
          viewBox="0 0 25 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M13.502 4.99951C13.502 4.44723 13.0543 3.99951 12.502 3.99951C11.9497 3.99951 11.502 4.44723 11.502 4.99951V13.0857L8.70906 10.2928C8.31854 9.90229 7.68537 9.90229 7.29485 10.2928C6.90432 10.6833 6.90432 11.3165 7.29485 11.707L11.0878 15.5C11.8688 16.281 13.1352 16.281 13.9162 15.5L17.7072 11.709C18.0977 11.3185 18.0977 10.6853 17.7072 10.2948C17.3167 9.90425 16.6835 9.90425 16.293 10.2948L13.502 13.0858V4.99951Z"
            fill="currentColor"
          />
          <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M4.5 14C5.05228 14 5.5 14.4477 5.5 15V17C5.5 17.5523 5.94772 18 6.5 18H18.5C19.0523 18 19.5 17.5523 19.5 17V15C19.5 14.4477 19.9477 14 20.5 14C21.0523 14 21.5 14.4477 21.5 15V17C21.5 18.6569 20.1569 20 18.5 20H6.5C4.84315 20 3.5 18.6569 3.5 17V15C3.5 14.4477 3.94772 14 4.5 14Z"
            fill="currentColor"
          />
        </svg>
      </div>

      <ng-template
        cdkConnectedOverlay
        [cdkConnectedOverlayOrigin]="mExportOptions"
        [cdkConnectedOverlayOpen]="openExportOptions()"
        [cdkConnectedOverlayPositions]="[
          {
            originX: 'center',
            originY: 'bottom',
            overlayX: 'center',
            overlayY: 'top',
            offsetY: 5
          }
        ]"
      >
        <ul
          class="p-2 flex flex-col justify-between shadow-lg rounded-xl border border-primary-border dark:border-dark-primary-border bg-base-400 dark:bg-dark-base-400"
          (clickOutside)="openExportOptions.set(false)"
        >
          <li
            class="flex px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg hover:bg-base-100 dark:hover:bg-dark-base-100"
            (click)="exportUsingS3()"
          >
            <div
              class="flex items-center justify-between text-[16px] font-medium !text-base-content dark:!text-dark-base-content"
            >
              AWS S3
            </div>
          </li>
          <li
            class="flex px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg hover:bg-base-100 dark:hover:bg-dark-base-100"
          >
            <div
              class="flex items-center justify-between text-[16px] font-medium !text-base-content dark:!text-dark-base-content"
            >
              SFTP
            </div>
          </li>
        </ul>
      </ng-template>

      <div
        class="w-10 h-10 rounded-full flex items-center justify-center bg-white dark:bg-black border border-primary-border dark:border-dark-primary-border"
      >
        <app-svg-icon
          type="icFilter"
          class="w-6 h-6 !text-neutral-content dark:!text-dark-neutral-content cursor-pointer"
          (click)="viewFilter.set(true)"
        ></app-svg-icon>
      </div>
    </div>
  </div>

  <div
    class="flex flex-col overflow-hidden"
    style="height: calc(100dvh - 198px)"
  >
    <app-conversation-list
      class="overflow-auto"
      style="height: calc(100dvh - 198px)"
      [class.hidden]="!isChatListActive()"
      [class.lg:block]="true"
      [conversations]="conversations()"
      [selectedConversationId]="selectedConversationId()"
      [isLoadingConversation]="isLoadingConversation()"
      [nameConversationNew]="nameConversationNew()"
      [hasMoreData]="hasMoreData()"
      [totalConversations]="totalConversations()"
      [searchContent]="searchContent()"
      [searchName]="searchName()"
      [timeFrom]="timeFrom()"
      [timeTo]="timeTo()"
      [listTagSelectedInConversation]="listTagSelectedInConversation()"
      [shouldPaginate]="true"
      [openExportOptions]="openExportOptions()"
      [isChatListActive]="isChatListActive()"
      (conversationSelected)="onConversationSelected($event)"
      (mConversationSelectedViewed)="mConversationSelectedViewed($event)"
      (scrolled)="onConversationScrolled()"
      (nameConversationNewChange)="nameConversationNew.set($event)"
      (conversationsUpdated)="onConversationsUpdated($event)"
      (conversationStatusChanged)="onConversationStatusChanged($event)"
    ></app-conversation-list>
  </div>
</div>

<app-mobile-drawer [visible]="viewFilter()">
  <div class="w-full h-full bg-base-100 dark:bg-dark-base-100">
    <app-mobile-header
      [title]="'Filter'"
      [backFn]="closeFn"
      [hideMenu]="true"
    ></app-mobile-header>
    <div class="w-full h-full flex flex-col p-4 pt-18 gap-y-3">
      <dx-form-field
        class="w-full"
        [style.margin-bottom]="0"
        [subscriptHidden]="true"
      >
        <dx-label>Content</dx-label>
        <input
          dx-input
          type="text"
          placeholder="Search by Content"
          [ngModel]="filterKeyWord()"
          (ngModelChange)="filterKeyWord.set($event)"
          (input)="onContentSearchChange($event)"
        />
        <app-svg-icon
          dxPrefix
          type="icSearch"
          class="ml-3 w-6 h-6 !text-neutral-content dark:!text-dark-neutral-content"
        ></app-svg-icon>
      </dx-form-field>

      <div class="w-full flex flex-col items-center gap-y-3">
        <div class="relative w-full">
          <div
            class="text-[15px] text-neutral-content dark:text-dark-neutral-content mb-[6px]"
          >
            From
          </div>
          <div
            class="modern-date-field w-full bg-base-400 dark:bg-dark-base-400 rounded-xl border border-gray-300 dark:border-gray-700 overflow-hidden"
          >
            <mat-form-field
              appearance="outline"
              class="w-full custom-mat-form-field"
            >
              <input
                matInput
                [matDatepicker]="pickerFrom"
                [value]="timeFrom()"
                (dateChange)="onDateFromMatChange($event)"
                class="py-2 px-3"
                [max]="timeTo()"
              />
              <mat-datepicker-toggle
                matIconSuffix
                [for]="pickerFrom"
              ></mat-datepicker-toggle>
              <mat-datepicker
                #pickerFrom
                [dateClass]="dateClass"
              ></mat-datepicker>
            </mat-form-field>
          </div>
        </div>

        <div class="relative w-full">
          <div
            class="text-[15px] text-neutral-content dark:text-dark-neutral-content mb-[6px]"
          >
            To
          </div>
          <div
            class="modern-date-field w-full bg-base-400 dark:bg-dark-base-400 rounded-xl border border-gray-300 dark:border-gray-700 overflow-hidden"
          >
            <mat-form-field
              appearance="outline"
              class="w-full custom-mat-form-field"
            >
              <input
                matInput
                [matDatepicker]="pickerTo"
                [value]="timeTo()"
                (dateChange)="onDateToMatChange($event)"
                class="py-2 px-3"
                [min]="timeFrom()"
                [max]="timeNow()"
              />
              <mat-datepicker-toggle
                matIconSuffix
                [for]="pickerTo"
              ></mat-datepicker-toggle>
              <mat-datepicker
                #pickerTo
                [dateClass]="dateClass"
              ></mat-datepicker>
            </mat-form-field>
          </div>
        </div>
      </div>

      <dx-form-field
        class="w-full"
        [style.margin-bottom]="0"
        [subscriptHidden]="true"
      >
        <dx-label>Status</dx-label>
        <dx-select
          [ngModel]="filterStatus()"
          (ngModelChange)="filterStatus.set($event); doSearch()"
          placeholder="Select status"
        >
          @for (status of listStatus; track status.code) {
          <dx-option [value]="status.code">{{ status.label }}</dx-option>
          }
        </dx-select>
      </dx-form-field>
      <dx-form-field
        class="w-full"
        [style.margin-bottom]="0"
        [subscriptHidden]="true"
      >
        <dx-label>Tags</dx-label>
        <dx-select
          [ngModel]="filterTags()"
          (ngModelChange)="selectTags($event)"
          placeholder="Search by Tags"
        >
          <dx-option [value]="0">All</dx-option>
          @for (tag of listTagForConversationFiltered(); track tag.id) {
          <dx-option [value]="tag.id">{{ tag.name }}</dx-option>
          } @empty {
          <dx-option [value]="">No option found</dx-option>
          }
        </dx-select>
      </dx-form-field>
    </div>
  </div>
</app-mobile-drawer>

<app-mobile-drawer [visible]="viewMDetailConv()">
  <div class="w-full h-screen bg-base-100 dark:bg-dark-base-100">
    <div class="w-full p-4 flex items-center justify-between space-x-4">
      <div class="flex-1 w-full flex items-center space-x-3">
        <ng-icon
          (click)="viewMDetailConv.set(!viewMDetailConv())"
          name="heroChevronLeft"
          class="text-2xl !text-neutral-content dark:!text-dark-neutral-content"
        ></ng-icon>
        <div class="flex-1 w-full flex flex-col">
          <div class="w-full">
            <span
              class="text-[16px] leading-none text-base-content dark:text-dark-base-conten font-bold line-clamp-1"
            >
              {{ selectedConversation()?.name }}
            </span>
          </div>
          <div class="w-full">
            <span
              class="text-[13px] text-neutral-content dark:text-dark-neutral-content line-clamp-1"
              >Last message:
              {{
                (selectedConversation()?.last_message_at
                  ? selectedConversation()?.last_message_at + "Z"
                  : null
                ) | date : "dd/MM/yyyy HH:mm"
              }}</span
            >
          </div>
        </div>
      </div>
      @if (selectedConversation()?.assigned && selectedConversation()?.assigned
      !== 'Unassigned') {
      <button dxButton="elevated" (click)="onResumeConversation()">
        Resume
      </button>
      } @if (!selectedConversation()?.assigned ||
      selectedConversation()?.assigned === 'Unassigned') {
      <button dxButton (click)="onTakeOverConversation()">Take over</button>
      }
    </div>
    <div class="w-full h-full-nav flex flex-col p-4 py-0 gap-y-3">
      <div
        class="bg-base-200 dark:bg-dark-base-200 rounded-2xl border border-primary-border dark:border-dark-primary-border"
      >
        <app-chat-content
          class=""
          [class.hidden]="isChatListActive()"
          [class.lg:block]="true"
          [conversationId]="selectedConversationId()"
          [showMessageInbox]="showMessageInbox()"
          [isLoadingContentConversation]="isLoadingContentConversation()"
          [chatMessages]="chatMessages()"
          [isTyping]="isTyping()"
          [isChatWithAI]="isChatWithAI()"
          [message]="message()"
          [isSendingMessage]="isSendingMessage()"
          [listTagSelectedInConversation]="listTagSelectedInConversation()"
          [conversationStatusInfo]="conversationStatusInfo()"
          (messageChange)="onMessageChange($event)"
          (addTag)="onAddTag()"
          (openSaveFAQDialog)="onOpenSaveFAQDialog($event)"
          (messagesUpdated)="onMessagesUpdated($event)"
          (sendingStatusChange)="onSendingStatusChange($event)"
          (reloadChatMessages)="onReloadChatMessages()"
        ></app-chat-content>
      </div>
    </div>
  </div>
</app-mobile-drawer>
}
