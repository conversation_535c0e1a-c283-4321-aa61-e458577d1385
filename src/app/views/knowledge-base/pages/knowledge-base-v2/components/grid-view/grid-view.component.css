/* Host component styling */
:host {
  display: block;
  height: 100%;
  overflow: hidden;
}

/* Virtual scroll viewport styling */
cdk-virtual-scroll-viewport {
  display: block;
  position: relative;
  overflow: auto !important;
  contain: none !important;
  transform: translateZ(0);
  will-change: scroll-position;
  -webkit-overflow-scrolling: touch;
  height: 100% !important;
  width: 100% !important;
  flex: 1 1 auto !important;
}

.cdk-virtual-scroll-content-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  contain: none !important;
  width: 100% !important;
  overflow: visible !important;
}

.cdk-virtual-scroll-spacer {
  position: absolute;
  top: 0;
  left: 0;
  height: 1px;
  width: 1px;
  transform-origin: 0 0;
}

/* Grid layout styling */
.file-folder-list-wrapper {
  width: 100%;
  padding: 10px;
  display: grid;
  grid-gap: 20px;
}

/* Folder and file item styling */
.file-folder-wrapper {
  padding: 0 8px !important;
  border-radius: 8px !important;
  transition: all 0.2s ease !important;
  pointer-events: auto;
  width: 100%;
  gap: 8px;
}

.folder-icon,
.folder-name {
  pointer-events: auto !important;
  cursor: pointer !important;
}

/*.file-folder-wrapper:hover {
  background-color: rgba(0, 0, 0, 0.04) !important;
}

.dark .file-folder-wrapper:hover {
  background-color: rgba(255, 255, 255, 0.05) !important;
}*/

/* Selected folder/file styling */
.folder-selected {
  background-color: rgba(114, 65, 255, 0.1) !important;
  border: 1px solid var(--color-light-primary) !important;
}

.dark .folder-selected {
  background-color: rgba(114, 65, 255, 0.2) !important;
  border: 1px solid var(--color-dark-primary) !important;
}

/* Folder item styling */
.folder-item {
  cursor: pointer;
  height: 56px;
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin-bottom: 10px;
  transition: all 0.2s ease;
}

.folder-item:hover {
  /* transform: translateY(-2px); */
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.dark .folder-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* Menu trigger styling */
.menu-trigger {
  z-index: 10;
  position: relative;
  padding: 5px;
  border-radius: 50%;
  background-color: transparent;
  transition: background-color 0.2s ease;
}

.menu-trigger:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

.dark .menu-trigger:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* File item specific styling */
.file-item {
  transition: all 0.2s ease;
  border: 2px solid transparent;
}

.file-item:hover {
  /* transform: translateY(-2px); */
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: rgba(59, 130, 246, 0.2);
}

.dark .file-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.file-item.selected {
  border-color: var(--color-light-primary);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.dark .file-item.selected {
  border-color: var(--color-dark-primary);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}

/* File status indicators */
.status-badge {
  font-size: 10px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border-radius: 12px;
  padding: 2px 8px;
  display: inline-block;
}

.status-completed {
  background-color: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.status-in-progress {
  background-color: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.status-pending {
  background-color: rgba(107, 114, 128, 0.1);
  color: #6b7280;
}

.status-error {
  background-color: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

/* Loading spinner */
.loading-spinner {
  border: 2px solid rgba(59, 130, 246, 0.2);
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* Smooth transitions */
* {
  transition: all 0.2s ease;
}

/* Focus states for accessibility */
.folder-item:focus,
.file-item:focus {
  outline: 2px solid var(--color-light-primary);
  outline-offset: 2px;
}

.dark .folder-item:focus,
.dark .file-item:focus {
  outline-color: var(--color-dark-primary);
}
