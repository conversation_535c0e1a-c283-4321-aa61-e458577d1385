import {SelectionModel} from '@angular/cdk/collections';
import {CdkConnectedOverlay, CdkOverlayOrigin} from '@angular/cdk/overlay';
import {CdkVirtualScrollViewport, ScrollingModule,} from '@angular/cdk/scrolling';
import {CommonModule, isPlatformBrowser} from '@angular/common';
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  inject,
  input,
  OnDestroy,
  output,
  PLATFORM_ID,
  signal,
  Signal,
  ViewChild,
} from '@angular/core';
import {DxTooltip} from '@dx-ui/ui';
import {SvgIconComponent} from '@shared/components';
import {ClickOutsideDirective, LongPressDirective} from '@shared/directives';
import {IFile, IFolder, ISearchModel} from '@shared/models';

@Component({
  selector: 'app-grid-view',
  standalone: true,
  imports: [
    CommonModule,
    ScrollingModule,
    DxTooltip,
    SvgIconComponent,
    ClickOutsideDirective,
    LongPressDirective,
    CdkOverlayOrigin,
    CdkConnectedOverlay
  ],
  templateUrl: './grid-view.component.html',
  styleUrl: './grid-view.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class GridViewComponent implements OnDestroy {
  private cdr = inject(ChangeDetectorRef);
  private platformId = inject(PLATFORM_ID);

  // Event listeners for cleanup
  private scrollListener?: () => void;
  private resizeListener?: () => void;

  // Signal-based inputs
  folderList = input<IFolder[]>([]);
  fileList = input<IFile[]>([]);
  searchModel = input.required<ISearchModel>();
  folderSelection = input.required<SelectionModel<IFolder>>();
  fileSelection = input.required<SelectionModel<IFile>>();
  isShiftPressed = input.required<Signal<boolean>>();
  isCtrlPressed = input.required<Signal<boolean>>();

  // Signal-based outputs
  folderSelected = output<{ folder: IFolder; event?: MouseEvent }>();
  fileSelected = output<{ file: IFile; event?: MouseEvent }>();
  folderDoubleClick = output<IFolder>();
  fileDoubleClick = output<IFile>();
  folderContextMenu = output<{ folder: IFolder; event: MouseEvent }>();
  fileContextMenu = output<{ file: IFile; event: MouseEvent }>();

  // New specific action outputs
  folderRename = output<any>();
  folderDelete = output<any>();
  fileInfo = output<any>();
  fileRename = output<any>();
  fileMove = output<any>();
  fileDelete = output<any>();
  refreshRequested = output<void>();

  @ViewChild('folderViewport', {static: false})
  folderViewport!: CdkVirtualScrollViewport;
  @ViewChild('fileViewport', {static: false})
  fileViewport!: CdkVirtualScrollViewport;

  // Context menu signals
  contextMenuItem = signal<
    { id: number; type: 'folder' | 'file'; item: IFolder | IFile } | undefined
  >(undefined);
  contextMenuPosition = signal<{ x: number; y: number } | undefined>(undefined);
  itemSelected = signal<{ id: number; type: 'folder' | 'file' } | undefined>(
    undefined
  );

  constructor() {
    // Close context menu on scroll or resize
    if (isPlatformBrowser(this.platformId)) {
      this.scrollListener = () => this.closeContextMenu();
      this.resizeListener = () => this.closeContextMenu();

      window.addEventListener('scroll', this.scrollListener, true);
      window.addEventListener('resize', this.resizeListener);
    }
  }

  // Event handlers
  onFolderClick(folder: IFolder, event?: MouseEvent) {
    console.log('🗂️ Grid View - Folder clicked:', {
      id: folder.id ?? 'undefined',
      name: folder.name,
      shiftPressed: this.isShiftPressed()(),
      ctrlPressed: this.isCtrlPressed()(),
      currentlySelected: this.folderSelection().selected.map(f => ({ id: f.id ?? 'undefined', name: f.name })),
      isCurrentlySelected: this.folderSelection().isSelected(folder)
    });
    this.folderSelected.emit({ folder, event });
  }

  // Debug method to check selection state
  checkFolderSelection(folder: IFolder): boolean {
    const isSelected = this.folderSelection().isSelected(folder);
    console.log('🔍 Grid View - Checking folder selection:', {
      folder: { id: folder.id ?? 'undefined', name: folder.name },
      isSelected,
      allSelected: this.folderSelection().selected.map(f => ({ id: f.id ?? 'undefined', name: f.name }))
    });
    return isSelected;
  }

  checkFileSelection(file: IFile): boolean {
    const isSelected = this.fileSelection().isSelected(file);
    console.log('🔍 Grid View - Checking file selection:', {
      file: { id: file.id ?? 'undefined', name: file.name },
      isSelected,
      allSelected: this.fileSelection().selected.map(f => ({ id: f.id ?? 'undefined', name: f.name }))
    });
    return isSelected;
  }

  onFileClick(file: IFile, event?: MouseEvent) {
    console.log('📄 Grid View - File clicked:', {
      id: file.id ?? 'undefined',
      name: file.name,
      shiftPressed: this.isShiftPressed()(),
      ctrlPressed: this.isCtrlPressed()(),
      currentlySelected: this.fileSelection().selected.map(f => ({ id: f.id ?? 'undefined', name: f.name }))
    });
    this.fileSelected.emit({ file, event });
  }

  onFolderDoubleClick(folder: IFolder) {
    this.folderDoubleClick.emit(folder);
  }

  onFileDoubleClick(file: IFile) {
    this.fileDoubleClick.emit(file);
  }

  onFolderContextMenu(folder: IFolder, event: MouseEvent) {
    this.folderContextMenu.emit({folder, event});
  }

  onFileContextMenu(file: IFile, event: MouseEvent) {
    this.fileContextMenu.emit({file, event});
  }

  // New context menu action methods
  onFolderRename(folder: IFolder) {
    this.folderRename.emit(folder);
  }

  onFolderDelete(folder: IFolder) {
    this.folderDelete.emit(folder);
  }

  onFileInfo(file: IFile) {
    this.fileInfo.emit(file);
  }

  onFileRename(file: IFile) {
    this.fileRename.emit(file);
  }

  onFileMove(file: IFile) {
    this.fileMove.emit(file);
  }

  onFileDelete(file: IFile) {
    this.fileDelete.emit(file);
  }

  onRefreshClick() {
    this.refreshRequested.emit();
  }

  onRightClick(item: IFolder | IFile, event: MouseEvent) {
    event.preventDefault();
    event.stopPropagation();

    const id = item.id || 0;
    // Check if item has file_path property to determine if it's a file
    const type = 'file_path' in item ? 'file' : 'folder';

    // Calculate menu position to avoid viewport overflow
    const menuWidth = type === 'folder' ? 245 : 166;
    const menuHeight = type === 'folder' ? 80 : 160;

    let x = event.clientX;
    let y = event.clientY;

    // Adjust position if menu would overflow viewport
    if (isPlatformBrowser(this.platformId)) {
      if (x + menuWidth > window.innerWidth) {
        x = window.innerWidth - menuWidth - 10;
      }

      if (y + menuHeight > window.innerHeight) {
        y = window.innerHeight - menuHeight - 10;
      }
    }

    // Set context menu data
    this.contextMenuItem.set({id, type, item});
    this.contextMenuPosition.set({x, y});
  }

  closeContextMenu() {
    this.contextMenuItem.set(undefined);
    this.contextMenuPosition.set(undefined);
  }

  // Utility methods
  trackByFolderId(_index: number, folder: IFolder): number {
    return folder.id || 0;
  }

  trackByFileId(_index: number, file: IFile): number {
    return file.id || 0;
  }

  removeSelectedFolderOrFile() {
    this.folderSelection().clear();
    this.fileSelection().clear();
    this.cdr.detectChanges();
  }

  refreshVirtualScrollViewports() {
    if (this.folderViewport) {
      this.folderViewport.checkViewportSize();
      this.folderViewport.scrollToIndex(0);
    }

    if (this.fileViewport) {
      this.fileViewport.checkViewportSize();
      this.fileViewport.scrollToIndex(0);
    }

    this.cdr.detectChanges();
  }

  getFileIcon(file: IFile): 'icFile' | 'icFilePdf' | 'icFileCsv' | 'icFileTxt' | 'icFileMd' | 'icLinkPrefix' {
    const extension = file.ext?.toLowerCase();
    switch (extension) {
      case 'pdf':
        return 'icFilePdf';
      case 'csv':
        return 'icFileCsv';
      case 'txt':
        return 'icFileTxt';
      case 'md':
        return 'icFileMd';
      case 'url':
        return 'icLinkPrefix';
      default:
        return 'icFile';
    }
  }

  getFileTypeColor(file: IFile): string {
    const extension = file.ext?.toLowerCase();
    switch (extension) {
      case 'pdf':
        return '#ef4444'; // red
      case 'csv':
        return '#10b981'; // green
      case 'txt':
        return '#6b7280'; // gray
      case 'md':
        return '#8b5cf6'; // purple
      default:
        return '#3b82f6'; // blue
    }
  }

  getStatusColor(status: string): string {
    switch (status) {
      case 'COMPLETED':
        return 'success'; // green
      case 'IN_PROGRESS':
        return 'warning'; // yellow
      case 'PENDING':
        return 'gray'; // gray
      case 'FAILED':
        return 'error'; // red
      case 'ERROR':
        return 'error'; // red
      default:
        return 'info'; // gray
    }
  }

  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  ngOnDestroy() {
    // Cleanup event listeners
    if (isPlatformBrowser(this.platformId)) {
      if (this.scrollListener) {
        window.removeEventListener('scroll', this.scrollListener, true);
      }
      if (this.resizeListener) {
        window.removeEventListener('resize', this.resizeListener);
      }
    }
  }

  openMenuActions(item: IFolder | IFile) {
    const id = item.id || 0;
    const type = item.isFolder === false ? 'file' : 'folder';
    this.itemSelected.set({id, type});
    // Close context menu when opening regular menu
    this.contextMenuItem.set(undefined);
    this.contextMenuPosition.set(undefined);
  }

  getType(item: IFolder | IFile): 'file' | 'folder' {
    return item.isFolder === false ? 'file' : 'folder';
  }

  closeAllMenus() {
    this.itemSelected.set(undefined);
    this.contextMenuItem.set(undefined);
    this.contextMenuPosition.set(undefined);
  }
}
