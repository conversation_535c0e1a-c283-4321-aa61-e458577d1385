<div class="py-3 h-full flex flex-col overflow-hidden">
  <div
    class="text-base-content dark:text-dark-base-content w-full h-full flex flex-col"
    [ngStyle]="{ height: 'calc(100% - 20px)', overflow: 'hidden' }"
  >
    <div
      class="flex flex-col flex-grow overflow-hidden"
      [ngStyle]="{ height: 'calc(100% - 52px)' }"
    >
      <div
        class="list-header text-neutral-content dark:text-dark-neutral-content"
      >
        <div class="list-cell name !font-semibold !text-sm">
          <div class="flex items-center justify-center">
            <p>Name</p>
            <div class="ml-2">
              <div
                class="flex items-center justify-center p-1 hover:cursor-pointer hover:bg-light-hover dark:hover:bg-dark-hover hover:rounded-3xl"
              >
                @if (nameOrder() === 'DESC') {
                <app-svg-icon
                  (click)="changeFilter('ASC', 'name')"
                  (mouseup)="$event.stopPropagation()"
                  type="icChevronDown"
                  class="w-4 h-4 text-blue-500"
                ></app-svg-icon>
                } @if (nameOrder() === 'ASC') {
                <app-svg-icon
                  (click)="changeFilter('DESC', 'name')"
                  (mouseup)="$event.stopPropagation()"
                  type="icChevronUp"
                  class="w-4 h-4 text-blue-500"
                ></app-svg-icon>
                }
              </div>
            </div>
          </div>
        </div>
        <div class="list-cell date">
          <div class="flex items-center justify-center">
            <p>Created</p>
            <div class="ml-2">
              <div
                class="flex items-center justify-center p-1 hover:cursor-pointer hover:bg-light-hover dark:hover:bg-dark-hover hover:rounded-3xl"
              >
                @if (createOrder() === 'DESC') {
                <app-svg-icon
                  (click)="changeFilter('ASC', 'created_at')"
                  (mouseup)="$event.stopPropagation()"
                  type="icChevronDown"
                  class="w-4 h-4 text-blue-500"
                ></app-svg-icon>
                } @if (createOrder() === 'ASC') {
                <app-svg-icon
                  (click)="changeFilter('DESC', 'created_at')"
                  (mouseup)="$event.stopPropagation()"
                  type="icChevronUp"
                  class="w-4 h-4 text-blue-500"
                ></app-svg-icon>
                }
              </div>
            </div>
          </div>
        </div>
        <div class="list-cell date">
          <div class="flex items-center justify-center">
            <p>Last update</p>
            <div class="ml-2">
              <div
                class="flex items-center justify-center p-1 hover:cursor-pointer hover:bg-light-hover dark:hover:bg-dark-hover hover:rounded-3xl"
              >
                @if (updateOrder() === 'DESC') {
                <app-svg-icon
                  (click)="changeFilter('ASC', 'updated_at')"
                  (mouseup)="$event.stopPropagation()"
                  type="icChevronDown"
                  class="w-4 h-4 text-blue-500"
                ></app-svg-icon>
                } @if (updateOrder() === 'ASC') {
                <app-svg-icon
                  (click)="changeFilter('DESC', 'updated_at')"
                  (mouseup)="$event.stopPropagation()"
                  type="icChevronUp"
                  class="w-4 h-4 text-blue-500"
                ></app-svg-icon>
                }
              </div>
            </div>
          </div>
        </div>
        <div class="list-cell status">
          <div class="flex items-center justify-between">Status</div>
        </div>
        <div class="list-cell actions"></div>
      </div>

      <cdk-virtual-scroll-viewport
        #listViewport
        id="listViewport"
        itemSize="50"
        minBufferPx="200"
        maxBufferPx="400"
        style="
          height: calc(100% - 128px);
          overflow: auto;
          width: 100%;
          flex-grow: 1;
        "
        class="h-full"
      >
        <ng-container
          *cdkVirtualFor="
            let item of combinedList();
            let i = index;
            trackBy: trackByItemId
          "
        >
          <!-- Folder Row -->
          @if (isFolder(item)) {
          <div
            (dblclick)="onFolderDoubleClick(item)"
            appLongPress
            (longPress)="onFolderDoubleClick(item)"
            (click)="onFolderClick(item, $event)"
            (contextmenu)="onRightClick(item, $event)"
            [ngClass]="{
              'folder-selected': folderSelection().isSelected($any(item)),
              'border-b border-primary-border dark:border-dark-primary-border hover:cursor-pointer hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover': !folderSelection().isSelected($any(item))
            }"
            [attr.data-folder-id]="item.id"
            class="list-row"
          >
            <div
              class="list-cell name"
              (dblclick)="$event.stopPropagation(); onFolderDoubleClick(item)"
            >
              <app-svg-icon
                type="icFolderFilled"
                class="w-6 h-6 flex items-center justify-center"
              ></app-svg-icon>
              <p
                class="truncate folder-name text-base-content dark:text-dark-base-content"
                [dxTooltip]="item.name"
                dxTooltipPosition="above"
              >
                {{ item.name }}
                @if (folderSelection().isSelected($any(item))) {
                  <span class="ml-2 text-xs bg-blue-500 text-white px-1 rounded">✓</span>
                }
              </p>
            </div>
            <div
              class="list-cell date text-base-content dark:text-dark-base-content"
              [dxTooltip]="getFormattedDateTime(item, 'create')"
              [dxTooltipPosition]="'left'"
            >
              {{ getFormattedDate(item, "create") }}
            </div>
            <div
              class="list-cell date text-base-content dark:text-dark-base-content"
              [dxTooltip]="getFormattedDateTime(item, 'update')"
              [dxTooltipPosition]="'left'"
            >
              {{ getFormattedDate(item, "update") }}
            </div>
            <div class="list-cell status"></div>

            <div class="list-cell actions">
              <app-svg-icon
                type="icMoreHorizontal"
                cdkOverlayOrigin
                #trigger="cdkOverlayOrigin"
                (click)="$event.stopPropagation(); openMenuActions(item)"
                class="cursor-pointer w-6 h-6 !text-neutral-content dark:!text-dark-neutral-content"
              >
              </app-svg-icon>
              <ng-template
                cdkConnectedOverlay
                [cdkConnectedOverlayOrigin]="trigger"
                [cdkConnectedOverlayOpen]="
                  itemSelected()?.id === item.id &&
                  itemSelected()?.type === getType(item)
                "
                [cdkConnectedOverlayPush]="true"
                [cdkConnectedOverlayPositions]="[
                  {
                    originX: 'start',
                    originY: 'center',
                    overlayX: 'end',
                    overlayY: 'top',
                    offsetY: 10
                  },
                  {
                    originX: 'start',
                    originY: 'center',
                    overlayX: 'end',
                    overlayY: 'bottom',
                    offsetY: 10,
                  },
                ]"
              >
                <ul
                  class="w-[245px] flex flex-col justify-between p-1 z-50 shadow-lg rounded-xl !text-base-content dark:!text-dark-base-content border border-primary-border dark:border-dark-primary-border bg-base-400 dark:bg-dark-base-400"
                  (clickOutside)="closeAllMenus()"
                >
                  <li>
                    <button
                      (click)="$event.stopPropagation(); onFolderRename(item)"
                      class="flex w-full px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover"
                    >
                      <app-svg-icon
                        type="icEdit"
                        class="w-6 h-6 flex items-center justify-center"
                      ></app-svg-icon>
                      <div
                        class="flex items-center justify-between text-[16px] font-medium"
                      >
                        Rename
                      </div>
                    </button>
                  </li>
                  <li>
                    <button
                      (click)="$event.stopPropagation(); onFolderDelete(item)"
                      class="flex w-full px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover"
                    >
                      <app-svg-icon
                        type="icTrash"
                        class="w-6 h-6 flex items-center justify-center"
                      ></app-svg-icon>
                      <div
                        class="flex items-center justify-between text-[16px] font-medium"
                      >
                        Delete
                      </div>
                    </button>
                  </li>
                </ul>
              </ng-template>
            </div>
          </div>
          } @if (isFile(item)) {
          <div
            class="list-row"
            (dblclick)="onFileDoubleClick(item); $event.stopPropagation()"
            appLongPress
            (longPress)="onFileDoubleClick(item)"
            (click)="onFileClick(item, $event)"
            (contextmenu)="onRightClick(item, $event)"
            [ngClass]="{
              'file-selected': fileSelection().isSelected($any(item)),
              'border-b border-primary-border dark:border-dark-primary-border hover:cursor-pointer hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover': !fileSelection().isSelected($any(item))
            }"
            draggable="true"
            [attr.data-file-id]="item.id"
          >
            <div class="list-cell name flex-shrink-0">
              <div class="flex flex-none items-center justify-center">
                @switch($any(item).ext) { @case ('CSV') {
                <app-svg-icon type="icFileCsv" class="w-6 h-6"></app-svg-icon>
                } @case ('MD') {
                <app-svg-icon type="icFileMd" class="w-6 h-6"></app-svg-icon>
                } @case ('TXT') {
                <app-svg-icon type="icFileTxt" class="w-6 h-6"></app-svg-icon>
                } @case ('PDF') {
                <app-svg-icon type="icFilePdf" class="w-6 h-6"></app-svg-icon>
                } @case ('URL') {
                <app-svg-icon
                  type="icLinkPrefix"
                  class="w-6 h-6 !text-neutral-content dark:!text-dark-neutral-content"
                ></app-svg-icon>
                } @default {
                <app-svg-icon
                  type="icFile"
                  class="w-6 h-6 !text-[#6F767E]"
                ></app-svg-icon>
                } }
              </div>
              <p
                class="flex-grow truncate text-base-content dark:text-dark-base-content"
                [dxTooltip]="item.name"
                dxTooltipPosition="above"
              >
                {{ item.name }}
                @if (fileSelection().isSelected($any(item))) {
                  <span class="ml-2 text-xs bg-green-500 text-white px-1 rounded">✓</span>
                }
              </p>
            </div>
            <div
              class="list-cell date text-base-content dark:text-dark-base-content"
              [dxTooltip]="getFormattedDateTime(item, 'create')"
              [dxTooltipPosition]="'left'"
            >
              {{ getFormattedDate(item, "create") }}
            </div>
            <div
              class="list-cell date text-base-content dark:text-dark-base-content"
              [dxTooltip]="getFormattedDateTime(item, 'update')"
              [dxTooltipPosition]="'left'"
            >
              {{ getFormattedDate(item, "update") }}
            </div>
            <div class="list-cell status">
              @switch ($any(item).status) { @case ('COMPLETED') {
              <div
                class="px-2 py-1 rounded-full bg-success dark:bg-success text-success-content dark:text-success-content text-[13px] font-semibold"
              >
                Completed
              </div>
              } @case ('IN_PROGRESS') {
              <div
                class="px-2 py-1 rounded-full bg-info dark:bg-info text-info-content dark:text-info-content text-[13px] font-semibold"
              >
                Processing
              </div>
              } @case ('ERROR') {
              <div
                class="px-2 py-1 rounded-full bg-error dark:bg-error text-error-content dark:text-error-content text-[13px] font-semibold"
              >
                Failed
              </div>
              } @case ('PENDING') {
              <div
                class="px-2 py-1 rounded-full bg-warning dark:bg-warning text-warning-content dark:text-warning-content text-[13px] font-semibold"
              >
                Pending
              </div>
              } @default {
              <div
                class="px-2 py-1 rounded-full bg-base-300 dark:bg-dark-base-300 text-base-content dark:text-dark-base-content text-[13px] font-semibold"
              >
                -
              </div>
              } }
            </div>
            <div class="list-cell actions">
              <app-svg-icon
                type="icMoreHorizontal"
                cdkOverlayOrigin
                #trigger="cdkOverlayOrigin"
                (click)="$event.stopPropagation(); openMenuActions(item)"
                class="w-6 h-6 !text-neutral-content dark:!text-dark-neutral-content cursor-pointer"
              >
              </app-svg-icon>
              <ng-template
                cdkConnectedOverlay
                [cdkConnectedOverlayOrigin]="trigger"
                [cdkConnectedOverlayOpen]="
                  itemSelected()?.id === item.id &&
                  itemSelected()?.type === getType(item)
                "
                [cdkConnectedOverlayPush]="true"
                [cdkConnectedOverlayPositions]="[
                  {
                    originX: 'start',
                    originY: 'center',
                    overlayX: 'end',
                    overlayY: 'top',
                    offsetY: 10
                  },
                  {
                    originX: 'start',
                    originY: 'center',
                    overlayX: 'end',
                    overlayY: 'bottom',
                    offsetY: 10,
                  },
                ]"
              >
                <ul
                  class="w-[166px] flex flex-col justify-between p-1 z-50 shadow-lg rounded-xl !text-base-content dark:!text-dark-base-content border border-primary-border dark:border-dark-primary-border bg-base-400 dark:bg-dark-base-400"
                  (clickOutside)="closeAllMenus()"
                >
                  <li>
                    <button
                      (click)="$event.stopPropagation(); onFileInfo(item)"
                      class="flex w-full px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover"
                    >
                      <app-svg-icon
                        type="icInfo"
                        class="w-6 h-6 flex items-center justify-center"
                      ></app-svg-icon>
                      <div
                        class="flex items-center justify-between text-[16px] font-medium"
                      >
                        Info
                      </div>
                    </button>
                  </li>
                  <li>
                    <button
                      (click)="$event.stopPropagation(); onFileRename(item)"
                      class="flex w-full px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover"
                    >
                      <app-svg-icon
                        type="icEdit"
                        class="w-6 h-6 flex items-center justify-center"
                      ></app-svg-icon>
                      <div
                        class="flex items-center justify-between text-[16px] font-medium"
                      >
                        Rename
                      </div>
                    </button>
                  </li>
                  <li>
                    <button
                      (click)="$event.stopPropagation(); onFileMove(item)"
                      class="flex w-full px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover"
                    >
                      <app-svg-icon
                        type="icFolderMove"
                        class="w-6 h-6 flex items-center justify-center"
                      ></app-svg-icon>
                      <div
                        class="flex items-center justify-between text-[16px] font-medium"
                      >
                        Move
                      </div>
                    </button>
                  </li>
                  <li>
                    <button
                      (click)="$event.stopPropagation(); onFileDelete(item)"
                      class="flex w-full px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover"
                    >
                      <app-svg-icon
                        type="icTrash"
                        class="w-6 h-6 flex items-center justify-center"
                      ></app-svg-icon>
                      <div
                        class="flex items-center justify-between text-[16px] font-medium"
                      >
                        Delete
                      </div>
                    </button>
                  </li>
                </ul>
              </ng-template>
            </div>
          </div>
          }
        </ng-container>
      </cdk-virtual-scroll-viewport>
    </div>
  </div>
</div>

@if (contextMenuItem() && contextMenuPosition()) {
<div
  class="fixed inset-0 z-40"
  (click)="closeContextMenu()"
  style="background: transparent"
></div>

<div
  class="fixed z-50"
  [style.left.px]="contextMenuPosition()!.x"
  [style.top.px]="contextMenuPosition()!.y"
>
  @if (contextMenuItem()!.type === 'folder') {
  <ul
    class="w-[245px] flex flex-col justify-between p-1 shadow-lg rounded-xl !text-base-content dark:!text-dark-base-content border border-primary-border dark:border-dark-primary-border bg-base-400 dark:bg-dark-base-400"
    (clickOutside)="closeContextMenu()"
  >
    <li>
      <button
        (click)="
          $event.stopPropagation();
          onFolderRename(contextMenuItem()!.item);
          closeContextMenu()
        "
        class="flex w-full px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover"
      >
        <app-svg-icon
          type="icEdit"
          class="w-6 h-6 flex items-center justify-center"
        ></app-svg-icon>
        <div class="flex items-center justify-between text-[16px] font-medium">
          Rename
        </div>
      </button>
    </li>
    <li>
      <button
        (click)="
          $event.stopPropagation();
          onFolderDelete(contextMenuItem()!.item);
          closeContextMenu()
        "
        class="flex w-full px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover"
      >
        <app-svg-icon
          type="icTrash"
          class="w-6 h-6 flex items-center justify-center"
        ></app-svg-icon>
        <div class="flex items-center justify-between text-[16px] font-medium">
          Delete
        </div>
      </button>
    </li>
  </ul>
  } @else {
  <ul
    class="w-[166px] flex flex-col justify-between p-1 shadow-lg rounded-xl !text-base-content dark:!text-dark-base-content border border-primary-border dark:border-dark-primary-border bg-base-400 dark:bg-dark-base-400"
    (clickOutside)="closeContextMenu()"
  >
    <li>
      <button
        (click)="
          $event.stopPropagation();
          onFileInfo(contextMenuItem()!.item);
          closeContextMenu()
        "
        class="flex w-full px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover"
      >
        <app-svg-icon
          type="icInfo"
          class="w-6 h-6 flex items-center justify-center"
        ></app-svg-icon>
        <div class="flex items-center justify-between text-[16px] font-medium">
          Info
        </div>
      </button>
    </li>
    <li>
      <button
        (click)="
          $event.stopPropagation();
          onFileRename(contextMenuItem()!.item);
          closeContextMenu()
        "
        class="flex w-full px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover"
      >
        <app-svg-icon
          type="icEdit"
          class="w-6 h-6 flex items-center justify-center"
        ></app-svg-icon>
        <div class="flex items-center justify-between text-[16px] font-medium">
          Rename
        </div>
      </button>
    </li>
    <li>
      <button
        (click)="
          $event.stopPropagation();
          onFileMove(contextMenuItem()!.item);
          closeContextMenu()
        "
        class="flex w-full px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover"
      >
        <app-svg-icon
          type="icFolderMove"
          class="w-6 h-6 flex items-center justify-center"
        ></app-svg-icon>
        <div class="flex items-center justify-between text-[16px] font-medium">
          Move
        </div>
      </button>
    </li>
    <li>
      <button
        (click)="
          $event.stopPropagation();
          onFileDelete(contextMenuItem()!.item);
          closeContextMenu()
        "
        class="flex w-full px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover"
      >
        <app-svg-icon
          type="icTrash"
          class="w-6 h-6 flex items-center justify-center"
        ></app-svg-icon>
        <div class="flex items-center justify-between text-[16px] font-medium">
          Delete
        </div>
      </button>
    </li>
  </ul>
  }
</div>
}
