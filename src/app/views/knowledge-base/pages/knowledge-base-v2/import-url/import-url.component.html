<div class="h-full relative flex flex-col rounded-3xl">
  <div
    class="absolute top-0 left-0 right-0 z-2 w-full flex items-center justify-between px-6 py-5 border-b border-primary-border dark:border-dark-primary-border bg-base-100 dark:bg-dark-base-100"
  >
    <div
      class="flex items-center text-2xl font-bold text-base-content dark:text-dark-base-content"
    >
      Import from URL
    </div>
    <div class="flex items-center justify-end space-x-4">
      <app-svg-icon
        type="icClose"
        class="w-6 h-6 !text-neutral-content dark:!text-dark-neutral-content cursor-pointer"
        (click)="this.dialogRef.close()"
      ></app-svg-icon>
    </div>
  </div>
  <div class="flex-1 overflow-y-auto mt-18 mb-20">
    <div
      [formGroup]="formGroup"
      class="px-6 pt-6 pb-[3px] flex flex-col space-x-4 bg-base-200 dark:bg-dark-base-200"
    >
      <dx-form-field class="w-full">
        <dx-label>Enter the URL of the page you want to import</dx-label>
        <input
          dxInput
          formControlName="url"
          [type]="'text'"
          placeholder="URL address, e.g. https://dxgpt.ai"
        />
        @if (this.formGroup.get('url')?.errors &&
        this.formGroup.get('url')?.errors?.['required'] &&
        (this.formGroup.get('url')?.touched ||
        this.formGroup.get('url')?.dirty)) {
        <dx-error>This field is required.</dx-error>
        } @if (this.formGroup.get('url')?.errors &&
        this.formGroup.get('url')?.errors?.['urlValidator'] &&
        (this.formGroup.get('url')?.touched ||
        this.formGroup.get('url')?.dirty)) {
        <dx-error>Please enter a valid URL.</dx-error>
        }
      </dx-form-field>
      @if (collectionList().length > 0) {
      <dx-form-field class="w-full">
        <dx-label class="text-sm">Collection</dx-label>
        <dx-select
          formControlName="collectionOfFile"
          placeholder="Select collection"
        >
          <dx-option
            *ngFor="let collection of collectionList(); track: collection.id"
            [value]="collection.id"
            >{{ collection.name }}</dx-option
          >
        </dx-select>
      </dx-form-field>
      }

      <!-- Mode Radio Group -->
      <div class="mt-4 w-full">
        <mat-radio-group
          aria-label="Select an option"
          class="flex flex-col bg-base-400 dark:bg-dark-base-400 rounded-[16px]"
          formControlName="modeGetUrl"
        >
          <mat-radio-button
            labelPosition="after"
            value="getOne"
            class="rounded-lg border-2 border-light-border-line dark:border-dark-border-line py-2 pl-4 pr-2 cursor-pointer"
          >
            <div
              class="w-full justify-start text-light-text dark:text-dark-text"
            >
              <h5 class="font-semibold">Individual URL</h5>
              <p>Import the entire content of a single webpage.</p>
            </div>
          </mat-radio-button>
          <mat-radio-button
            labelPosition="after"
            value="getAll"
            class="rounded-lg border-2 border-light-border-line dark:border-dark-border-line py-2 pl-4 pr-2 cursor-pointer"
          >
            <div
              class="w-full justify-start text-light-text dark:text-dark-text"
            >
              <h5 class="font-semibold">Site-wide (all URLs)</h5>
              <p>
                Import all the webpages of a website. Each webpage will be
                imported as a separate Article.
              </p>
            </div>
          </mat-radio-button>
        </mat-radio-group>
      </div>
    </div>
  </div>

  <div
    class="absolute left-0 bottom-0 right-0 z-2 flex items-center justify-end space-x-4 px-6 py-5 border-t border-primary-border dark:border-dark-primary-border bg-base-200 dark:bg-dark-base-200"
  >
    <button dxButton="elevated" (click)="this.dialogRef.close()">Close</button>
    <button
      dxLoadingButton="filled"
      [loading]="isImportingUrl()"
      [disabled]="this.formGroup.invalid || isImportingUrl()"
      (click)="importUrl()"
    >
      Import {{ isImportingUrl() ? "ing..." : "" }}
    </button>
  </div>
</div>
