import { CommonModule } from '@angular/common';
import { Component, inject, OnInit } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { NgIconsModule, provideIcons } from '@ng-icons/core';
import { heroXCircle } from '@ng-icons/heroicons/outline';
import { heroInformationCircleSolid } from '@ng-icons/heroicons/solid';
import { CustomIconComponent } from '@shared/components';
import { IAgent } from '@shared/models/agent.model';

@Component({
  selector: 'app-agent-details-dialog',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    NgIconsModule,
    CustomIconComponent,
  ],
  templateUrl: './agent-details-dialog.component.html',
  providers: [
    provideIcons({
      heroXCircle,
      heroInformationCircleSolid,
    }),
  ],
})
export class AgentDetailsDialogComponent implements OnInit {
  private dialogRef = inject(MatDialogRef<AgentDetailsDialogComponent>);
  private data: { agent: IAgent } = inject(MAT_DIALOG_DATA);

  agent: IAgent;

  constructor() {
    this.agent = this.data.agent;
  }

  ngOnInit(): void {}

  close(): void {
    this.dialogRef.close();
  }
}
